<?xml version="1.0" encoding="UTF-8"?>
<!-- Keeping this file in the classpath enables the automatic configuration.
     More details: https://logging.apache.org/log4j/2.x/manual/configuration.html -->
<Configuration status="INFO"> <!-- The log level for the log4j2 internal classes. -->
    <Appenders>
        <Console name="CONSOLE" target="SYSTEM_OUT">
            <PatternLayout pattern= "%d{dd MMM yyyy HH:mm:ss,SSS} [%p] %X{RequestId} (%t) %c: %m%n"/>
        </Console>
    </Appenders>

    <Loggers>
        <Root level="WARN"> <!-- Sets the general log level. -->
            <AppenderRef ref="CONSOLE" />
        </Root>

        <!-- Allowing the test classes to be more verbose:  -->
        <Logger name="com.amazon.wehcmfacade.*" level="INFO" />
    </Loggers>
</Configuration>
