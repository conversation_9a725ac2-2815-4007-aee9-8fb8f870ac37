import json
from concurrent import futures
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from typing import Dict, List, Optional

from aws_lambda_powertools import Logger
from aws_lambda_powertools.metrics import MetricUnit
from json_logic import convert_jl_2_dsl_query
from requests import ConnectionError, Timeout
from sepal.metric_utils import record_single_metric
from sepal.model.collision_detection import CollisionEmployeeResult, CollisionResult
from sepal.model.self_service_rule import Rule
from sepal.sapi.sapi_mapper import map_period_sapi_fields_to_es
from sepal.sdk.employee_index_client import EmployeeIndexClient
from sepal.utils.self_service_rule_resolver import SelfServiceRuleResolver
from sepal.x_ray_utils import xray_operation
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_fixed, wait_random

from ads_collision_detection_service.service.collision_helpers.exceptions import (
    JsonLogicToDslException,
)

RETURNED_EMPLOYEE_COUNT = 50


def resolve_rule(rule: Rule, rule_map: Dict[str, Rule]) -> dict:
    resolved_def = json.loads(
        rule.ruleDefinition,
        cls=SelfServiceRuleResolver,
        rules=rule_map,
        resolving_rule=rule,
        lob_name_mapping=True,
    )
    rule.set_resolved_definition(resolved_def)
    return resolved_def


def get_elastic_search_query_for_collision_detection(query: dict) -> str:
    dsl_query = {
        "size": RETURNED_EMPLOYEE_COUNT,
        "query": {
            "nested": {
                "path": "periods",
                "query": query,
                "inner_hits": {"_source": ["periods.startDate"]},
            }
        },
        "post_filter": {
            "script": {
                "script": {
                    "source": "doc['_id'].value.length() < 9 || "
                    "(doc['_id'].value.length() == 9 && "
                    "  (doc['_id'].value.indexOf('1') == 0 || doc['_id'].value.indexOf('2') == 0))",
                    "lang": "painless",
                }
            }
        },
        "sort": [{"_id": {"order": "desc"}}],
        "_source": False,
    }
    return json.dumps(dsl_query)


class EmployeeIndexCollisionChecker:
    """
    This class should not be reused. Example usage:
    with EmployeeIndexCollisionChecker(ei_client, logger) as checker:
        collided_rules = checker.check_collision(new_rule, potentials_rules)
        if not collided_rules:
            # Success case, no collision
        else:
            # Handle collided_rules
    """

    def __init__(
        self,
        ei_client: EmployeeIndexClient,
        rule_map: Dict[str, Rule],
        logger: Logger,
        max_rules_to_check: int,
        max_thread_count,
    ):
        """
        :param ei_client: The EI Client
        """
        self._rule_map = rule_map
        self._ei_client = ei_client
        self._logger = logger
        self._max_rules_to_check = max_rules_to_check
        self._max_thread_count = max_thread_count
        self._found_collisions = []

    def __enter__(self):
        self._found_collisions = []
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self._found_collisions = []

    @xray_operation("CheckPotentialRuleCollisions")
    def check_collisions(
        self,
        new_rule: Rule,
        potential_collision_rules: List[Rule],
        small_rule_first=True,
    ) -> List[CollisionResult]:
        if small_rule_first and len(potential_collision_rules) > 1:
            potential_collision_rules.sort(key=lambda r: len(r.ruleDefinition))
        new_rule_def = resolve_rule(new_rule, self._rule_map)
        thread_count = min(self._max_thread_count, len(potential_collision_rules))
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            future_to_rule = {
                executor.submit(self._check_collision, new_rule, new_rule_def, rule): rule
                for rule in potential_collision_rules
            }
            exc: Optional[Exception] = None
            exc_rule: Optional[Rule] = None
            for future in futures.as_completed(future_to_rule):
                rule = future_to_rule[future]
                try:
                    collision_info = future.result()
                    if collision_info and collision_info.totalHits > 0:
                        self._found_collisions.append(collision_info)
                        if -1 < self._max_rules_to_check <= len(self._found_collisions):
                            return self._found_collisions
                except Exception as ex:
                    self._logger.exception(f"Failed to check collision with rule {rule.rulePath}")
                    exc = ex
                    exc_rule = rule

            # If any found collisions, return them
            if self._found_collisions:
                return self._found_collisions

            # if there is an exception with any of the rules,
            # even if self._found_collisions is empty, we cannot assume that we're safe
            if exc:
                self._logger.error(f"Failed to check collision with rule {exc_rule.rulePath}")
                # For now, we still have some operators that we're not supported yet,
                # so we will ignore this translation error
                if not isinstance(exc, JsonLogicToDslException):
                    raise exc
                return []

            # no exception + no collision found
            return []

    @retry(
        wait=wait_fixed(1) + wait_random(min=0, max=0.5),
        stop=stop_after_attempt(10),
        reraise=True,
        retry=retry_if_exception_type((ConnectionError, Timeout)),
    )
    @xray_operation("CheckSingleRuleCollision")
    def _check_collision(
        self,
        new_rule: Rule,
        new_rule_def: dict,
        current_rule: Rule,
    ) -> Optional[CollisionResult]:
        if -1 < self._max_rules_to_check <= len(self._found_collisions):
            return None
        current_rule_def = resolve_rule(current_rule, self._rule_map)
        jl_query = {"and": [new_rule_def, current_rule_def]}
        try:
            dsl_query_str = convert_jl_2_dsl_query(
                definition=jl_query, var_field_mapper=map_period_sapi_fields_to_es
            )
        except Exception as e:
            record_single_metric(
                operation="CheckSingleRuleCollision",
                metric_name="JsonLogicToDslExceptionCount",
                metric_unit=MetricUnit.Count,
                value=1,
            )
            raise JsonLogicToDslException(
                f"Failed to convert JL to DSL for {json.dumps(jl_query)}, error {e}", e
            )
        try:
            ei_query = get_elastic_search_query_for_collision_detection(json.loads(dsl_query_str))
        except Exception as e:
            self._logger.error(f"Invalid DSL error {e}, translated DSL {dsl_query_str}")
            raise
        headers = {
            "rule-name": f"ADS_Collision_Detection#{new_rule.rulePath}__{current_rule.rulePath}#YES"
        }
        response = self._ei_client.multi_search(query=[ei_query], headers=headers)[0]
        self._logger.info(
            {
                "message": f"EI transaction for rule {new_rule.rulePath} vs {current_rule.rulePath}",
                "jl_query": jl_query,
                "ei_query": ei_query,
                "response": response,
            }
        )
        total_hits = response.get("totalHits", 0)
        employees = []
        if total_hits > 0:
            employee_data = response.get("employees_data", [])
            employees = [
                CollisionEmployeeResult(
                    employeeId=employee["employeeId"],
                    totalCollisionPeriods=len(employee["periods"]) if "periods" in employee else 0,
                    firstPeriod=employee["periods"][0]["startDate"] if "periods" in employee
                                                                       and employee["periods"] else "",
                )
                for employee in employee_data
            ]
        return CollisionResult(
            rulePath=current_rule.rulePath, totalHits=total_hits, employees=employees
        )
