package.ADSCollisionDetectionService = {
    interfaces = (1.0);

    # See https://w.amazon.com/?BrazilPython3
    build-system = brazilpython;
    build-tools = {
        1.0 = {
            BrazilPython = 3.0;
        };
    };

    dependencies = {
        1.0 = {
          Python-aws-lambda-powertools = 2.x;
          # Explicit replace the Python-pydantic 1.8.2, https://code.amazon.com/packages/Python-pydantic/releases
          Python-pydantic = 1.10.x;
          SepalServerlessPythonUtils = 1.0;
          WEPopsSelfServiceLambdaPythonClient = 1.0;
          WEPopsSelfServiceLambdaModel = 1.0;

          Python-gremlinpython = 3.5.x;
          Boto3 = 1.x;

          Python-tenacity = 8.x;
          AmazonCodeGuruProfilerPythonAgent = 1.x;
          Python-dateutil = 2.x;
          Python-json-logic = 0.x;
          SecurityEventsPythonLambdaSupport = 1.0;
        };
    };

    test-dependencies = {
        1.0 = {
            # Run tests with py.test in BrazilPython.
            BrazilPython-Pytest = 6.x;

            # Coverage for Python tests.
            Python-Pytest-cov = 4.x;
            Coverage = 7.x;

            # Enable the guard command to watch tests and automatically re-run them
            BrazilPython-Pytest-Guard = any;

            # Publish test results to Brazil's test and coverage detection
            BrazilPythonTestSupport = 3.0;

            # Sphinx documentation with Amazon customizations so that
            # links on code.amazon.com work
            Python-amazon-doc-utils = 1.0;
            Sphinx = 5.x;

            # Dependencies for formatting checks. Controlled in `setup.cfg` and
            # `pyproject.toml`
            BrazilPython-formatters = 1.0;
            Python-black = no1;
            Python-isort = 5.x;

            # Dependencies for type checking. Controlled in `setup.cfg` and
            # `pyproject.toml`
            BrazilPython-mypy = any;
            Python-mypy = 1.x;

            # Dependencies for build-time linting. Controlled in `setup.cfg`
            BrazilPython-Flake8 = any;
            Python-flake8 = 5.x;
            Python-pep8-naming = 0.x;
            Python-moto = 3.x;
        };
    };

    resolves-conflict-dependencies = {
        1.0 = {
            # Explicit replace the Python-pydantic 1.8.2, https://code.amazon.com/packages/Python-pydantic/releases
            Python-pydantic = 1.10.x;
            PyYAML = 6.x;
            Python-cryptography = 3.2.x;
        };
    };

    targets = {
        # see setup.py for the actual build logic
        # keep this line for brazil-path to work
        python = { type = python; };
    };
};
