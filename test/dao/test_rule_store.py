import json
import pathlib
from typing import Any, Dict

import boto3
import pytest
from aws_lambda_powertools import Logger
from moto import mock_dynamodb
from sepal.model.constants import RuleStatus, RuleType

from ads_collision_detection_service.dao import rule_store
from ads_collision_detection_service.dao.rule_store import RuleStore

rule_store.MAX_RULES_RESULTS = 1  # do this to test multiple pages
RULES_TABLE_NAME = "WePopsSelfService-test-Rules"
logger = Logger(service="test")


def setup_rule_store():
    dynamodb = boto3.client("dynamodb", region_name="us-west-2")
    dynamodb.create_table(
        TableName=RULES_TABLE_NAME,
        KeySchema=[
            {"AttributeName": "rulePath", "KeyType": "HASH"},
            {"AttributeName": "ruleSelector", "KeyType": "RANGE"},
        ],
        AttributeDefinitions=[
            {"AttributeName": "rulePath", "AttributeType": "S"},
            {"AttributeName": "ruleSelector", "AttributeType": "S"},
            {"AttributeName": "ruleType", "AttributeType": "S"},
        ],
        GlobalSecondaryIndexes=[
            {
                "IndexName": "ruleType-ruleSelector-index",
                "KeySchema": [
                    {"AttributeName": "ruleType", "KeyType": "HASH"},
                    {"AttributeName": "ruleSelector", "KeyType": "RANGE"},
                ],
                "Projection": {"ProjectionType": "ALL"},
            },
            {
                "IndexName": "ruleType-rulePath-index",
                "KeySchema": [
                    {"AttributeName": "ruleType", "KeyType": "HASH"},
                    {"AttributeName": "rulePath", "KeyType": "RANGE"},
                ],
                "Projection": {"ProjectionType": "ALL"},
            },
        ],
        BillingMode="PAY_PER_REQUEST",
    )
    with pathlib.Path(__file__).parent.parent.joinpath("test_data").joinpath(
        "test_rules.json"
    ).open("rt", encoding="utf-8") as f:
        test_rules = json.load(f)
        for rule in test_rules:
            dynamodb.put_item(TableName=RULES_TABLE_NAME, Item=rule)
    return test_rules, RuleStore(
        boto3.resource("dynamodb", region_name="us-west-2"), RULES_TABLE_NAME, logger
    )


def _get_rule_path(ddb_raw: Dict[str, Any]) -> str:
    return ddb_raw["rulePath"]["S"]


@mock_dynamodb
def test_get_rule_details():
    test_rules, rs = setup_rule_store()
    rule_path = _get_rule_path(test_rules[0])
    rule = rs.get_rule_details(rule_path)
    assert rule_path == rule.rulePath
    assert RuleStatus.LIVE == rule.ruleStatus


@mock_dynamodb
def test_get_rules_page():
    _, rs = setup_rule_store()
    rules, last_evaluated_key = rs.get_rules_page(RuleStatus.LIVE, RuleType.PG, None, 2, None)
    assert 2 == len(rules)
    assert isinstance(last_evaluated_key, dict)


@mock_dynamodb
def test_fetch_all_live_rules():
    test_rules, rs = setup_rule_store()
    test_live_rule_paths = set()
    for test_rule in test_rules:
        if test_rule["ruleStatus"]["S"] == "LIVE":
            test_live_rule_paths.add(_get_rule_path(test_rule))

    live_rules = rs.fetch_all_rules(RuleType.PG, RuleStatus.LIVE)
    db_live_rule_paths = set(live_rules.keys())
    assert test_live_rule_paths == db_live_rule_paths


@mock_dynamodb
def test_to_rules_with_invalid_format():
    rule = {"rulePath": "test"}
    with pytest.raises(KeyError) as err_info:
        rs = RuleStore(
            boto3.resource("dynamodb", region_name="us-west-2"), RULES_TABLE_NAME, logger
        )
        rs.to_rules([rule])
    assert "'ruleType'" == str(err_info.value)
