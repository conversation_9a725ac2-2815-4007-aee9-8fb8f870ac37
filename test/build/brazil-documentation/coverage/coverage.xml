<?xml version="1.0" ?>
<coverage version="7.2.7" timestamp="1750171151072" lines-valid="636" lines-covered="551" line-rate="0.8664" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.2.7 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source></source>
	</sources>
	<packages>
		<package name=".Volumes.workplace.temp.ADSCollisionDetectionService.src.ADSCollisionDetectionService.src.ads_collision_detection_service" line-rate="0.9355" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/__init__.py" complexity="0" line-rate="0.3333" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="0"/>
						<line number="3" hits="0"/>
					</lines>
				</class>
				<class name="sel.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/sel.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name=".Volumes.workplace.temp.ADSCollisionDetectionService.src.ADSCollisionDetectionService.src.ads_collision_detection_service.client" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/client/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="wepops_self_service_client.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/client/wepops_self_service_client.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="44" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="69" hits="1"/>
						<line number="76" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name=".Volumes.workplace.temp.ADSCollisionDetectionService.src.ADSCollisionDetectionService.src.ads_collision_detection_service.constants" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/constants/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="env_vars.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/constants/env_vars.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
					</lines>
				</class>
				<class name="rule_constants.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/constants/rule_constants.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name=".Volumes.workplace.temp.ADSCollisionDetectionService.src.ADSCollisionDetectionService.src.ads_collision_detection_service.dao" line-rate="0.9605" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/dao/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="rule_store.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/dao/rule_store.py" complexity="0" line-rate="0.9605" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="45" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="0"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="71" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="105" hits="1"/>
						<line number="108" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name=".Volumes.workplace.temp.ADSCollisionDetectionService.src.ADSCollisionDetectionService.src.ads_collision_detection_service.lambda_handlers" line-rate="0.876" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/lambda_handlers/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="collision_detection_approval.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/lambda_handlers/collision_detection_approval.py" complexity="0" line-rate="0.8904" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="17" hits="1"/>
						<line number="26" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="62" hits="0"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="74" hits="1"/>
						<line number="77" hits="1"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="95" hits="1"/>
						<line number="111" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="131" hits="1"/>
						<line number="135" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="146" hits="1"/>
						<line number="147" hits="1"/>
						<line number="152" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="168" hits="1"/>
						<line number="170" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
					</lines>
				</class>
				<class name="collision_detection_approval_sync.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/lambda_handlers/collision_detection_approval_sync.py" complexity="0" line-rate="0.8571" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="20" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="39" hits="1"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="55" hits="0"/>
						<line number="58" hits="1"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="76" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="106" hits="1"/>
						<line number="110" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name=".Volumes.workplace.temp.ADSCollisionDetectionService.src.ADSCollisionDetectionService.src.ads_collision_detection_service.service" line-rate="0.7196" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/service/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="ei_collision_checker_service.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/service/ei_collision_checker_service.py" complexity="0" line-rate="0.7196" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="29" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="1"/>
						<line number="60" hits="1"/>
						<line number="62" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="85" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="0"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="103" hits="1"/>
						<line number="105" hits="1"/>
						<line number="111" hits="1"/>
						<line number="126" hits="1"/>
						<line number="128" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="145" hits="1"/>
						<line number="147" hits="1"/>
						<line number="151" hits="1"/>
						<line number="152" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="161" hits="1"/>
						<line number="163" hits="1"/>
						<line number="169" hits="1"/>
						<line number="171" hits="1"/>
						<line number="173" hits="1"/>
						<line number="174" hits="1"/>
						<line number="177" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="193" hits="1"/>
						<line number="195" hits="1"/>
						<line number="207" hits="0"/>
						<line number="208" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="213" hits="0"/>
						<line number="215" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="223" hits="0"/>
						<line number="225" hits="0"/>
						<line number="231" hits="0"/>
						<line number="232" hits="0"/>
						<line number="233" hits="0"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="246" hits="0"/>
						<line number="248" hits="0"/>
						<line number="249" hits="0"/>
						<line number="253" hits="0"/>
						<line number="255" hits="1"/>
						<line number="266" hits="1"/>
						<line number="269" hits="1"/>
						<line number="272" hits="1"/>
						<line number="273" hits="1"/>
						<line number="274" hits="1"/>
						<line number="275" hits="1"/>
						<line number="277" hits="1"/>
						<line number="278" hits="1"/>
						<line number="280" hits="0"/>
						<line number="282" hits="1"/>
						<line number="284" hits="1"/>
						<line number="288" hits="1"/>
						<line number="289" hits="1"/>
						<line number="290" hits="1"/>
						<line number="291" hits="1"/>
						<line number="292" hits="1"/>
						<line number="294" hits="1"/>
						<line number="295" hits="1"/>
						<line number="296" hits="1"/>
						<line number="298" hits="1"/>
						<line number="299" hits="1"/>
						<line number="308" hits="1"/>
						<line number="309" hits="1"/>
						<line number="313" hits="1"/>
						<line number="316" hits="1"/>
						<line number="317" hits="1"/>
						<line number="319" hits="1"/>
						<line number="322" hits="1"/>
						<line number="323" hits="1"/>
						<line number="326" hits="1"/>
						<line number="327" hits="1"/>
						<line number="328" hits="1"/>
						<line number="331" hits="0"/>
						<line number="332" hits="0"/>
						<line number="333" hits="0"/>
						<line number="335" hits="1"/>
						<line number="338" hits="1"/>
						<line number="339" hits="1"/>
						<line number="340" hits="1"/>
						<line number="341" hits="1"/>
						<line number="348" hits="1"/>
						<line number="349" hits="1"/>
						<line number="350" hits="1"/>
						<line number="351" hits="1"/>
						<line number="355" hits="1"/>
						<line number="362" hits="1"/>
						<line number="365" hits="1"/>
						<line number="368" hits="1"/>
						<line number="369" hits="1"/>
						<line number="372" hits="1"/>
						<line number="374" hits="0"/>
						<line number="375" hits="0"/>
						<line number="380" hits="1"/>
						<line number="381" hits="1"/>
						<line number="382" hits="1"/>
						<line number="387" hits="1"/>
						<line number="388" hits="0"/>
						<line number="393" hits="0"/>
						<line number="394" hits="0"/>
						<line number="395" hits="0"/>
						<line number="396" hits="0"/>
						<line number="397" hits="0"/>
						<line number="398" hits="0"/>
						<line number="401" hits="0"/>
						<line number="402" hits="0"/>
						<line number="405" hits="0"/>
						<line number="406" hits="0"/>
						<line number="407" hits="0"/>
						<line number="411" hits="1"/>
						<line number="412" hits="1"/>
						<line number="416" hits="1"/>
						<line number="418" hits="1"/>
						<line number="424" hits="0"/>
						<line number="425" hits="0"/>
						<line number="427" hits="0"/>
						<line number="428" hits="0"/>
						<line number="430" hits="0"/>
						<line number="432" hits="0"/>
						<line number="438" hits="0"/>
						<line number="439" hits="0"/>
						<line number="440" hits="0"/>
						<line number="441" hits="0"/>
						<line number="442" hits="0"/>
						<line number="443" hits="0"/>
						<line number="446" hits="0"/>
						<line number="447" hits="0"/>
						<line number="450" hits="0"/>
						<line number="452" hits="0"/>
						<line number="453" hits="0"/>
						<line number="457" hits="0"/>
						<line number="459" hits="1"/>
						<line number="466" hits="1"/>
						<line number="473" hits="1"/>
						<line number="474" hits="1"/>
						<line number="475" hits="1"/>
						<line number="480" hits="1"/>
						<line number="482" hits="1"/>
						<line number="488" hits="1"/>
						<line number="489" hits="1"/>
						<line number="490" hits="1"/>
						<line number="491" hits="1"/>
						<line number="492" hits="1"/>
						<line number="493" hits="1"/>
						<line number="494" hits="1"/>
						<line number="495" hits="1"/>
						<line number="496" hits="1"/>
						<line number="497" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name=".Volumes.workplace.temp.ADSCollisionDetectionService.src.ADSCollisionDetectionService.src.ads_collision_detection_service.service.collision_helpers" line-rate="0.9692" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/service/collision_helpers/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines/>
				</class>
				<class name="ei_collision_checker.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/service/collision_helpers/ei_collision_checker.py" complexity="0" line-rate="0.9574" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="61" hits="1"/>
						<line number="64" hits="1"/>
						<line number="75" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="118" hits="1"/>
						<line number="119" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="122" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="129" hits="1"/>
						<line number="132" hits="1"/>
						<line number="133" hits="1"/>
						<line number="137" hits="1"/>
						<line number="138" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="0"/>
						<line number="143" hits="1"/>
						<line number="146" hits="1"/>
						<line number="148" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="169" hits="1"/>
						<line number="170" hits="1"/>
						<line number="176" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="184" hits="1"/>
						<line number="188" hits="1"/>
						<line number="189" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="199" hits="1"/>
						<line number="200" hits="1"/>
						<line number="201" hits="1"/>
						<line number="213" hits="1"/>
					</lines>
				</class>
				<class name="exceptions.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/service/collision_helpers/exceptions.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="3" hits="1"/>
						<line number="4" hits="1"/>
					</lines>
				</class>
				<class name="potential_collision_helper.py" filename="/Volumes/workplace/temp/ADSCollisionDetectionService/src/ADSCollisionDetectionService/src/ads_collision_detection_service/service/collision_helpers/potential_collision_helper.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="1" hits="1"/>
						<line number="2" hits="1"/>
						<line number="4" hits="1"/>
						<line number="5" hits="1"/>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="56" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
